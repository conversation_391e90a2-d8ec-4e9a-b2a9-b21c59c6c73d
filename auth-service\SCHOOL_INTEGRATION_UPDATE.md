# School Integration Update

## Overview
Pembaruan ini menambahkan integrasi yang lebih baik antara tabel `auth.user_profiles` dan `public.schools` dengan menambahkan field `school_id` sebagai foreign key.

## Perubahan yang Dibuat

### 1. Model UserProfile.js
- ✅ **Ditambahkan field `school_id`**:
  ```javascript
  school_id: {
    type: DataTypes.INTEGER,
    allowNull: true,
    field: 'school_id',
    references: {
      model: 'schools',
      key: 'id'
    },
    onUpdate: 'CASCADE',
    onDelete: 'SET NULL'
  }
  ```
- ✅ **Ditambahkan relasi ke School model**:
  ```javascript
  UserProfile.belongsTo(models.School, {
    foreignKey: 'school_id',
    as: 'school',
    onUpdate: 'CASCADE',
    onDelete: 'SET NULL'
  });
  ```
- ✅ **Ditambahkan index untuk school_id**
- ✅ **Ditambahkan method baru**:
  - `findBySchoolId(schoolId, options)` - Mencari user profiles berdasarkan school_id
  - `getSchoolDistribution()` - Mendapatkan distribusi user per sekolah

### 2. Model School.js
- ✅ **Ditambahkan relasi ke UserProfile**:
  ```javascript
  School.hasMany(models.UserProfile, {
    foreignKey: 'school_id',
    as: 'userProfiles',
    onUpdate: 'CASCADE',
    onDelete: 'SET NULL'
  });
  ```

### 3. Model User.js
- ✅ **Diperbaiki default value token_balance** dari environment variable ke 0 (sesuai struktur tabel)

### 4. Controller userController.js
- ✅ **Ditambahkan support untuk field `school_id`** dalam update profile
- ✅ **Ditambahkan include School** dalam query user profile
- ✅ **Ditambahkan endpoint baru**:
  - `GET /auth/schools/:schoolId/users` - Mendapatkan user berdasarkan school_id
  - `GET /auth/schools/distribution` - Mendapatkan distribusi user per sekolah

### 5. Middleware validation.js
- ✅ **Ditambahkan validasi untuk school_id**:
  ```javascript
  school_id: Joi.number().integer().positive()
  ```

### 6. Routes auth.js
- ✅ **Ditambahkan routes baru**:
  - `GET /auth/schools/:schoolId/users`
  - `GET /auth/schools/distribution`

## Struktur Tabel yang Didukung

### auth.users ✅
```sql
id              uuid PRIMARY KEY DEFAULT gen_random_uuid()
email           varchar NOT NULL
password_hash   varchar NOT NULL
token_balance   integer NOT NULL DEFAULT 0
created_at      timestamp DEFAULT now()
updated_at      timestamp DEFAULT now()
username        varchar
user_type       varchar NOT NULL DEFAULT 'user'
is_active       boolean NOT NULL DEFAULT true
last_login      timestamp
```

### auth.user_profiles ✅
```sql
user_id         uuid NOT NULL (FK to auth.users.id)
full_name       varchar
school_origin   varchar
date_of_birth   date
gender          varchar
created_at      timestamp DEFAULT now()
updated_at      timestamp DEFAULT now()
school_id       integer (FK to public.schools.id) -- BARU
```

### public.schools ✅
```sql
id              integer PRIMARY KEY (auto increment)
name            varchar NOT NULL
address         text
city            varchar
province        varchar
created_at      timestamp DEFAULT now()
```

## API Endpoints Baru

### 1. Get Users by School
```
GET /auth/schools/:schoolId/users?page=1&limit=20
```
Response:
```json
{
  "success": true,
  "data": {
    "userProfiles": [...],
    "pagination": {
      "total": 50,
      "page": 1,
      "limit": 20,
      "pages": 3
    }
  }
}
```

### 2. Get School Distribution
```
GET /auth/schools/distribution
```
Response:
```json
{
  "success": true,
  "data": {
    "schoolDistribution": [
      {
        "school_id": 1,
        "school_name": "SMA Negeri 1",
        "city": "Jakarta",
        "province": "DKI Jakarta",
        "user_count": 25,
        "percentage": 15.5
      }
    ]
  }
}
```

## Update Profile dengan School ID
```
PUT /auth/profile
```
Body:
```json
{
  "full_name": "John Doe",
  "school_id": 123,
  "school_origin": "SMA Negeri 1 Jakarta", // optional, untuk backward compatibility
  "date_of_birth": "1995-01-01",
  "gender": "male"
}
```

## Scripts Utilitas

### 1. Test Integration
```bash
node scripts/test-school-integration.js
```
Menguji apakah integrasi school_id berfungsi dengan benar.

### 2. Migrate Data
```bash
# Dry run (default)
node scripts/migrate-school-data.js

# Apply changes
DRY_RUN=false node scripts/migrate-school-data.js
```
Migrasi data dari school_origin ke school_id dengan mencocokkan nama sekolah.

## Backward Compatibility
- ✅ Field `school_origin` tetap ada untuk backward compatibility
- ✅ API endpoints lama tetap berfungsi
- ✅ Validasi mendukung kedua field (`school_origin` dan `school_id`)

## Rekomendasi Penggunaan
1. **Untuk data baru**: Gunakan `school_id` untuk referensi ke tabel schools
2. **Untuk data lama**: Jalankan script migrasi untuk mengkonversi `school_origin` ke `school_id`
3. **Untuk konsistensi**: Pertimbangkan untuk menghapus field `school_origin` setelah migrasi selesai

## Testing
Jalankan test untuk memastikan semua berfungsi:
```bash
npm test
node scripts/test-school-integration.js
```
